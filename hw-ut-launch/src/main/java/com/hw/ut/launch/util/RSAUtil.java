package com.hw.ut.launch.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-04-02 19:36
 */
@Slf4j
public class RSAUtil {

    public static final String KEY_ALGORITHM = "RSA";
    private static final int MAX_ENCRYPT_BLOCK = 117;
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * 随机生成密钥对，返回公钥、私钥
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2020-12-16 09:11
     **/
    public static Map<String, Object> genKeyPair() {
        try {
            final Map<String, Object> keyMap = new HashMap<>();
            // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
            // 初始化密钥对生成器，密钥大小为96-1024位
            keyPairGen.initialize(1024, new SecureRandom());
            // 生成一个密钥对，保存在keyPair中
            KeyPair keyPair = keyPairGen.generateKeyPair();
            // 得到私钥
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            // 得到公钥
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
            // 得到私钥字符串
            String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
            // 将公钥和私钥保存到Map
            //0表示公钥
            keyMap.put("publicKey", publicKeyString);
            //1表示私钥
            keyMap.put("privateKey", privateKeyString);
            log.info("-----publicKey = " + publicKeyString);
            log.info("-----privateKey = " + privateKeyString);
            return keyMap;
        } catch (NoSuchAlgorithmException e) {
            log.error("生成RSA密钥对异常", e);
        }
        return null;
    }


    public static byte[] encrypt(byte[] data, String publicKey) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 117) {
            byte[] cache;
            if (inputLen - offSet > 117) {
                cache = cipher.doFinal(data, offSet, 117);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    /**
     * 加密
     *
     * @param text      需要加密的字符串
     * @param publicKey 公钥
     * @return 加密后的字符串
     */
    public static String encrypt(String text, final String publicKey) {
        try {
            return Base64.encodeBase64String(encrypt(text.getBytes(StandardCharsets.UTF_8), publicKey));
        } catch (Exception e) {
            log.warn("加密失败");
        }
        return null;
    }


    public static byte[] decrypt(byte[] text, String privateKey) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, privateK);
        int inputLen = text.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;

        for (int i = 0; inputLen - offSet > 0; offSet = i * 128) {
            byte[] cache;
            if (inputLen - offSet > 128) {
                cache = cipher.doFinal(text, offSet, 128);
            } else {
                cache = cipher.doFinal(text, offSet, inputLen - offSet);
            }

            out.write(cache, 0, cache.length);
            ++i;
        }

        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }


    /**
     * 解密
     *
     * @param text       需要解密的密文
     * @param privateKey 私钥
     * @return 解密后的字符串
     */
    public static String decrypt(String text, final String privateKey) {
        try {
            return new String(decrypt(Base64.decodeBase64(text.getBytes(StandardCharsets.UTF_8)), privateKey), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("解密失败");
        }
        return null;
    }

    /**
     * 保存公钥字符串到文件中
     *
     * @param filePath : 文件路径
     * @return : 公钥
     * @throws Exception
     */
    public static void writePublicKeyStringToFile(String publicKeyString, String filePath) {
        try {
            FileUtils.writeStringToFile(new File(filePath), publicKeyString, String.valueOf(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存公钥字符串到文件中失败！");
        }
    }

    /**
     * 从文件中加载公钥字符串
     *
     * @param filePath : 文件路径
     * @return : 公钥
     * @throws Exception
     */
    public static String loadPublicKeyStringFromFile(String filePath) {
        try {
            // 将文件内容转为字符串
            return FileUtils.readFileToString(new File(filePath), String.valueOf(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取公钥文件字符串失败！");
        }
    }

    /**
     * 保存私钥字符串到文件中
     *
     * @param filePath : 文件路径
     * @return : 公钥
     * @throws Exception
     */
    public static void writePrivateKeyStringToFile(String privateKeyString, String filePath) {
        try {
            FileUtils.writeStringToFile(new File(filePath), privateKeyString, String.valueOf(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存私钥字符串到文件中失败！");
        }
    }

    /**
     * 从文件中加载私钥字符串
     *
     * @param filePath : 文件路径
     * @return : 公钥
     * @throws Exception
     */
    public static String loadPrivateKeyStringFromFile(String filePath) {
        try {
            // 将文件内容转为字符串
            return FileUtils.readFileToString(new File(filePath), String.valueOf(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取私钥文件字符串失败！");
        }
    }


//    public static void main(String[] args) {
//        Map<String, Object> map = genKeyPair();
//        assert map != null;
//        String publicKey = (String) map.get("publicKey");
//        String privateKey = (String) map.get("privateKey");
////        // 生成密钥对文件,在实际开发中，根据实际需求生成文件位置
//        String pubPath = "C:\\Users\\<USER>\\Desktop\\rsa\\publicKey.pub";
//        String priPath = "C:\\Users\\<USER>\\Desktop\\rsa\\privateKey.pri";
//        writePublicKeyStringToFile(publicKey, pubPath);
//        writePrivateKeyStringToFile(privateKey, priPath);
//        publicKey = loadPublicKeyStringFromFile(pubPath);
//        privateKey = loadPrivateKeyStringFromFile(priPath);
//        System.out.println("文件获取的公钥字符串：\n" + publicKey);
//        System.out.println("文件获取的私钥字符串：\n" + privateKey);
//        String content = "张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四张三 李四";
//        //  "http://localhost:8080/a.jpg";
//        String encryptContent = encrypt(content, publicKey);
//        System.out.println("公钥加密后的字符串：\n" + encryptContent);
//        assert encryptContent != null;
//        String decryptContent = decrypt(encryptContent, privateKey);
//        System.out.println("私钥解密后的字符串：\n" + decryptContent);
//    }

//    public static void main(String[] args) {
//        try {
//
//            String rsaPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHvLBAZBKx6yrLmZ1Lc33/tgIxVi/XwH25PgJppRGFfA5jMNbIWu1fczx4LYkBZwFThfy/hIE177KpKasRyoJutyDVS4+jSK+Fj9rZVNyGMoZZTLSgkzmWDC+JTpBoVyce+IATDZ3Th8bSnBWczF2/ItZ7lPCZBinmZjpYCo+9jwIDAQAB";
//            String rsaPrivateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIe8sEBkErHrKsuZnUtzff+2AjFWL9fAfbk+AmmlEYV8DmMw1sha7V9zPHgtiQFnAVOF/L+EgTXvsqkpqxHKgm63INVLj6NIr4WP2tlU3IYyhllMtKCTOZYML4lOkGhXJx74gBMNndOHxtKcFZzMXb8i1nuU8JkGKeZmOlgKj72PAgMBAAECgYBU5gkbjTxAOkX/KKrAiZEygoXmk/DCsqwwU/+wUkWeRxS8p8x/bpfvzqTwyhJaZTHoKvMGWvtyX+VQdzZ/nzlqz8M4hzr4zkliHTrgIZbgKAnOmAiBqIdSuQBkyf4zeDXKNg+w+UFEHb9+bnxQ3m0n+PoAipTX4nQHYPDK6jqcOQJBAP7XZtAvCOyl9p7kf4X9dt7urfu6cavxm8lWRGNSyoVD+/8hapkj7Kwo1jE2j782RHPPExwYP8wAV3SEiFejzSUCQQCIWqqtZIMk6+89UsW5OAtK6WjeG3qLxPAMTtmsM3JAEixi+/OGBIUBwNIuSZfXedbjWhxrclQ/c983AbYGQvOjAkA6sHbAGSTwAySx4SOxQvc+Ti+JWp3VIHZlPtXPHgEAAfEmB5caH5jg6SKzAMLIIYJQPW9EOB1xKN77OY6AyHt1AkAtKBmuIsNrztcEMA1bMbxLqtEX/XGwKCB3cwWTidYkceBRR692irihxAHXYnfu3GLWGFmhG9Paz7z0spRdrn6RAkAmvOwlYaTks85PqiWQNUaCxBPKGLm7OwnaH0N5nu4zO+B/XcCqtaFzfsuBrJYgLjW7MpSzkS3rAj7QJDIh2Ur4";
//            // 原始数据
//            String originalData = "Hello, RSA!";
//
//            String encryptedData = RSAUtil.encrypt(originalData, rsaPublicKey);
//            System.out.println("Encrypted Data: " + encryptedData);
//            String decryptedData = RSAUtil.decrypt(encryptedData, rsaPrivateKey);
//            System.out.println("Decrypted Data: " + decryptedData);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

}

