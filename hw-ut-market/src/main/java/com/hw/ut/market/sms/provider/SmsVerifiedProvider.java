package com.hw.ut.market.sms.provider;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.hw.ut.common.core.utils.SpringUtils;
import com.hw.ut.market.domain.Country;
import com.hw.ut.market.domain.ProviderCountry;
import com.hw.ut.market.domain.RegisterAuth;
import com.hw.ut.market.domain.RegisterPhone;
import com.hw.ut.market.service.ProviderCountryService;
import com.hw.ut.market.sms.enums.ProviderEnum;
import com.hw.ut.market.sms.enums.SmsResult;
import com.hw.ut.market.sms.exception.SmsException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/20  下午4:24
 */
@Slf4j
public class SmsVerifiedProvider implements SmsProvider {

    @Override
    public boolean releasePhone(RegisterAuth auth, RegisterPhone registerPhone) {
        return changeStatus(auth.getPassword(), registerPhone.getOptNo(), 8);
    }

    public boolean changeStatus(String token, String optNo, Integer status) {
        String url = null;
        String returnInfo;
        try {
            url = String.format("https://activate-api.smsverified.com/stubs/handler_api.php?api_key=%s&action=setStatus&id=%s&status=%s",
                    token,optNo, status);
            returnInfo = HttpUtil.createGet(url).execute().body();;
        } catch (Exception e) {
            log.info("SmsVerifiedProvider changeStatus error={}", url, e);
            throw new SmsException(SmsResult.PARAMETER_ERROR, "请求接口异常");
        }
        log.info("SmsVerifiedProvider changeStatus={},{}", url, returnInfo);
        if (StrUtil.isBlank(returnInfo)) {
            return false;
        }
        return returnInfo.startsWith("ACCESS_");
    }


    @Override
    public void requestPhone(RegisterAuth auth, RegisterPhone registerPhone) {

        ProviderCountry one = SpringUtils.getBean(ProviderCountryService.class).getOne(ProviderEnum.smsVerified.getCode(), registerPhone.getCountryCode());
        if (one == null) {
            throw new SmsException(SmsResult.PARAMETER_ERROR, "暂不支持该地区");
        }
        Pair<String, String> pair = requestPhone(auth.getPassword(), one.getPlatCountryCode());
        registerPhone.setPhone(pair.getKey());
        registerPhone.setOptNo(pair.getValue());
    }

    private Pair<String, String> requestPhone(String token, String countryId) {
        String url = null;
        String returnInfo;
        try {
            url = String.format("https://activate-api.smsverified.com/stubs/handler_api.php?api_key=%s&action=getNumber&country=%s&service=%s",
                    token, countryId, "tg");
            returnInfo = HttpUtil.createGet(url).execute().body();;
        } catch (Exception e) {
            log.info("SmsVerifiedProvider requestPhone error={}", url, e);
            throw new SmsException(SmsResult.PARAMETER_ERROR, "请求接口异常");
        }
        log.info("SmsVerifiedProvider requestPhone={},{}", url, returnInfo);
        if (StrUtil.isBlank(returnInfo)) {
            throw new SmsException(SmsResult.PARAMETER_ERROR, "获取手机失败");
        }
        //ACCESS_NUMBER:****************:***********
        String[] split = returnInfo.split(":");
        if (split.length >= 3 && StrUtil.isAllNotBlank(split[0], split[1], split[2])) {
            return Pair.of(split[2], split[1]);
        }
        if (returnInfo.contains("BAD_KEY")) {
            throw new SmsException(SmsResult.ACCOUNT_INVALID, "API密钥不正确");
        }
        if (returnInfo.contains("NO_NUMBERS")) {
            throw new SmsException(SmsResult.NONE_PHONE, "暂无号码");
        }
        if (returnInfo.contains("WRONG_SERVICE")) {
            throw new SmsException(SmsResult.NONE_PHONE, "暂无号码");
        }
        throw new SmsException(SmsResult.OTHER, returnInfo.split(":")[0]);
    }


    @Override
    public String getSmsCode(RegisterAuth auth, RegisterPhone registerPhone) {
        return getSmsCode(auth.getPassword(), registerPhone.getOptNo());
    }

    private String getSmsCode(String token, String optNo) {
        String url = null;
        String returnInfo;
        try {
            url = String.format("https://activate-api.smsverified.com/stubs/handler_api.php?api_key=%s&action=getStatus&id=%s",
                    token, optNo);
            returnInfo = HttpUtil.createGet(url).execute().body();;
        } catch (Exception e) {
            log.info("SmsVerifiedProvider getSmsCode error={}", url, e);
            return "";
        }
        log.info("SmsVerifiedProvider getSmsCode={},{}", url, returnInfo);
        if (StrUtil.isBlank(returnInfo)) {
            return "";
        }
        if (returnInfo.startsWith("STATUS_OK") && returnInfo.split(":").length >= 2) {
            return returnInfo.split(":")[1];
        }
        if (returnInfo.startsWith("STATUS_CANCEL")) {
            throw new SmsException(SmsResult.OTHER, "已取消");
        }
        return "";
    }

    @Override
    public void black(RegisterAuth auth, RegisterPhone registerPhone, String msg) {

    }

    @Override
    public List<Country> getCountries(List<Country> allCountry) {
        List<String> country = SpringUtils.getBean(ProviderCountryService.class).getCountry(ProviderEnum.smsVerified.getCode());
        return allCountry.stream().filter(item -> country.contains(item.getCountryCode())).collect(Collectors.toList());
    }

}
