package com.hw.ut.market.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;

/**
 * <AUTHOR> Administrator
 * @create 2024/9/9/009 19:46
 * @Description :
 */
@Getter
@AllArgsConstructor
public enum RegisterTaskStateEnum {

    INIT(0, "初始"),
    IN_PROGRESS(1, "进行中"),
    PAUSE(2, "暂停"),
    COMPLETE(3, "已完成"),
    END(4, "结束");

    Integer id;
    String name;

    public static RegisterTaskStateEnum of(Integer id) {

        return EnumSet.allOf(RegisterTaskStateEnum.class)
                .stream()
                .filter(p -> p.getId().equals(id))
                .findAny()
                .orElse(null);
    }
}
