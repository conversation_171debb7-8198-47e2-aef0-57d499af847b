package com.hw.ut.order.api.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hw.ut.common.core.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("商家排名查询")
public class PowerSpotSummaryDTO extends PageDomain {

    @ApiModelProperty("排序方式：1-充值金额，2-总消费,3-TG消费，4-WS消费，5-RCS消费")
    private Integer sortType;

    @ApiModelProperty("统计开始日期,格式 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty("统计结束日期,格式 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "统计开始日期,格式 yyyy-MM-dd HH:mm:ss",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentStartDateTime;

    @ApiModelProperty(value = "统计结束日期,格式 yyyy-MM-dd HH:mm:ss",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentEndDateTime;


}
