package com.hw.ut.robot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class TgGroupModifyHeaderImgDTO {

    @ApiModelProperty(value = "机器人号")
    @NotEmpty
    private String robotSerialNo;

    @ApiModelProperty(value = "群号")
    @NotEmpty
    private String chatroomSerialNo;

    @ApiModelProperty(value = "群头像url")
    @NotEmpty
    private String headerImg;
}
